from src.shared.gemini_client import Gemini<PERSON>ervice

from settings import SQL_SERVER, SQL_DATABASE, SQL_USERNAME, SQL_PASSWORD, STORAGE_CONNECTION_STRING, \
    GEMINI_MODEL_NAME
from src.shared.data_sources.azure_blob_client import AzureBlobReader
from src.shared.data_sources.db_connection import DatabaseClient
from src.shared.document_processor import DocumentProcessorService

config = {
    "db_type": "mssql",
    "server": SQL_SERVER,
    "database": SQL_DATABASE,
    "username": SQL_USERNAME,
    "password": SQL_PASSWORD,
}
db = DatabaseClient(config)
# Gemini service
gemini_client = GeminiService(model_name=GEMINI_MODEL_NAME)

doc_processor = DocumentProcessorService(db)

blob_client = AzureBlobReader(STORAGE_CONNECTION_STRING)
# extractor = DocumentFillingService(gemini_client)

