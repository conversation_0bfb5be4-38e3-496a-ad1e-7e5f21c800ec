import logging

def setup_logger(name=__name__, level=logging.INFO):
    # Configure root logger once
    if not logging.getLogger().hasHandlers():
        logging.basicConfig(
            level=logging.WARNING,  # Base fallback level
            format="%(asctime)s | %(levelname)s | %(name)s | %(message)s"
        )

        # Silence noisy external loggers
        for noisy_logger in ["httpx", "google_genai", "urllib3"]:
            logging.getLogger(noisy_logger).setLevel(logging.INFO)

        # Optional: set global app base logger
        logging.getLogger("src").setLevel(level)

    # Always set level for the requested logger
    logger = logging.getLogger(name)
    logger.setLevel(level)
    return logger
