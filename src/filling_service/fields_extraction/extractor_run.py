from typing import Dict
from src.classifier_service.common.utils.service_status_logging import _log_failure
from src.filling_service.config import extractor
from src.filling_service.utils import build_schema_model
from src.shared.service_registry import doc_processor
from src.shared.utils.logger_config import setup_logger

logger = setup_logger(__name__)

def execute_extraction_pipeline(doc_id: int, doc_type_id: int, preview_bytes: bytes) -> Dict[str, str]:
    """
    Executes field extraction for the given document using its type and preview content.

    Args:
        doc_id (int): Unique identifier of the document to process.
        doc_type_id (int): ID of the document type used to retrieve field configuration.
        preview_bytes (bytes): Preview content of the document (typically first few pages).

    Returns:
        Dict[str, str]: Dictionary of extracted field values, empty if failure occurs.
    """
    service_id = 3  # Service ID for extraction
    logger.info(f"[EXTRACTION] Starting field extraction for document ID: {doc_id}")
    doc_processor.log_document_service_status(doc_id, service_id=service_id, status_id=2)  # Status: Processing

    try:
        # Step 1: Fetch field configuration
        fields = doc_processor.get_fields_by_document_type_id(doc_type_id)
        if not fields:
            logger.warning(f"[FIELDS] No fields configured for document type ID {doc_type_id}. Skipping extraction.")
            _log_failure(doc_id, service_id, f"No fields configured for document type ID {doc_type_id}")
            return {}

        logger.info(f"[FIELDS] Fields to extract: {fields}")

        # Step 2: Build schema model dynamically
        schema_type = build_schema_model(fields)

        # Step 3: Extract field values using extractor
        extracted_field_values = extractor.extract_field_values(preview_bytes, fields, schema_type)
        if not extracted_field_values:
            logger.warning(f"[EXTRACTION] No field values extracted for document ID {doc_id}")
            _log_failure(doc_id, service_id, "Failed to extract field values.")
            return {}

        logger.info(f"[EXTRACTION] Successfully extracted fields for document ID {doc_id}")

        # Step 4: Insert extracted field values into the database
        for field_name, field_value in extracted_field_values.items():
            if field_value:
                doc_processor.insert_field_value_for_document(doc_id, field_name, field_value)
                logger.info(f"[DB] Inserted: {field_name} = {field_value}")
            else:
                logger.debug(f"[DB] Skipped empty field: {field_name}")

        logger.info(f"[DB] Field value insertion complete for document ID {doc_id}")
        return extracted_field_values

    except Exception as e:
        logger.error(f"[EXTRACTION] Extraction failed for document ID {doc_id}: {e}", exc_info=True)
        _log_failure(doc_id, service_id, str(e))
        return {}