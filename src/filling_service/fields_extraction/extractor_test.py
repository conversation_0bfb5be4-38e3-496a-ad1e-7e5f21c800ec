from typing import Dict
from src.classifier_service.common.utils.clean_storage_name import extract_blob_name
from src.filling_service.config import extractor
from src.filling_service.utils import build_schema_model
from src.shared.service_registry import doc_processor, blob_client
from src.shared.utils.logger_config import setup_logger

logger = setup_logger(__name__)


def execute_extraction_pipeline(doc_id: int) -> Dict[str, str]:
    """
    Orchestrates the field extraction and document naming pipeline for a given document ID.

    Args:
        doc_id (int): Unique identifier of the document to process.

    Returns:
        Dict[str, str]: Dictionary of extracted field values, empty if failure occurs.
    """
    logger.info(f"---- STARTING FILLING SERVICE FOR DOCUMENT ID: {doc_id} ----")

    try:
        # Step 1: Fetch document metadata
        email_info = doc_processor.get_document_info(doc_id)
        if not email_info:
            raise ValueError("Failed to retrieve document metadata.")

        document_location = email_info.get("storage_location")
        title = email_info.get("original_name", "")
        email_source = email_info.get("metadata", {}).get("source", "unknown")
        doc_type_id = email_info.get("document_type_id")
        template_id = email_info.get("company_data_source_id")

        logger.info(f"[META] Location: {document_location} | Title: {title} | Source: {email_source}")

        # Step 2: Get company info
        company_id = doc_processor.get_company_id_by_document_id(doc_id)
        container_name = f"company-{company_id}"
        blob_name = extract_blob_name(document_location)

        logger.info(f"[BLOB] Company ID: {company_id} | Container: {container_name} | Blob: {blob_name}")

        # Step 3: Fetch preview content
        preview_bytes = blob_client.get_document_preview(container_name, blob_name, num_pages=5)
        if not preview_bytes:
            raise ValueError(f"Preview content could not be fetched from blob storage: {document_location}")
        logger.info(f"[BLOB] Retrieved {len(preview_bytes)} bytes from preview")

        # Step 4: Fetch field configuration
        fields = doc_processor.get_fields_by_document_type_id(doc_type_id)
        if not fields:
            logger.warning(f"No fields configured for document type ID {doc_type_id}. Skipping extraction.")
            return {}
        logger.info(f"[FIELDS] Fields to extract: {fields}")

        # Step 5: Build schema and extract values
        schema_type = build_schema_model(fields)
        extracted_field_values = extractor.extract_field_values(preview_bytes, fields, schema_type)
        logger.info(f"[EXTRACTION] Completed extraction for document ID {doc_id}")

        # Step 6: Insert extracted field values into DB
        for field_name, field_value in extracted_field_values.items():
            if field_value:
                doc_processor.insert_field_value_for_document(doc_id, field_name, field_value)
                logger.info(f"[DB] Inserted: {field_name} = {field_value}")
            else:
                logger.info(f"[DB] Skipped empty field: {field_name}")

        logger.info(f"[DB] Field value insertion complete for document ID {doc_id}")

        # Step 7: Generate document name from extracted values
        try:
            naming_template = doc_processor.get_naming_template_by_document_type_and_destination(
                document_type_id=doc_type_id,  # Replace 101
                company_data_destination_id=template_id or 6  # Replace 6
            )

            if naming_template and extracted_field_values:
                logger.info(f"[NAMING] Using template: {naming_template}")
                doc_name = extractor.generate_document_name(preview_bytes, extracted_field_values, naming_template)

                doc_processor.log_document_name(doc_id, doc_name)
                logger.info(f"[NAMING] Generated and stored document name: {doc_name}")
            else:
                if not naming_template:
                    logger.warning(f"[NAMING] No naming template found for document type ID {doc_type_id}")
                if not extracted_field_values:
                    logger.warning(f"[NAMING] No extracted values available for naming")

        except Exception as naming_error:
            logger.error(f"[NAMING] Failed to generate document name for doc ID {doc_id}: {naming_error}", exc_info=True)

        return extracted_field_values

    except Exception as e:
        logger.error(f"[PIPELINE] Extraction failed for doc ID {doc_id}: {e}", exc_info=True)
        return {}

# Sample Usage
# result = execute_extraction_pipeline(268)
# print(result)
