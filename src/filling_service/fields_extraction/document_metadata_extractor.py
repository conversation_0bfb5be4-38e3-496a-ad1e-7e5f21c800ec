import json
import os
import re
import tempfile
from pathlib import Path
from typing import Optional, Tuple, List, Type, Dict, Union

from src.filling_service.project_matching.response_schema import FilingMetadata
from src.filling_service.prompts.base_prompt import extract_metadata_field_prompt, generate_doc_name_by_convention_prompt
from src.filling_service.prompts.project_matching_prompt import match_project_prompt
from src.shared.utils.logger_config import setup_logger

logger = setup_logger(__name__)


class DocumentFillingService:
    def __init__(self, gemini_client):
        self.gemini = gemini_client

    def _detect_file_type(self, document_bytes: bytes) -> Tuple[str, str]:
        if not document_bytes:
            return 'unknown', '.bin'

        if document_bytes.startswith(b'%PDF'):
            return 'pdf', '.pdf'
        elif document_bytes.startswith(b'\xff\xd8\xff'):
            return 'jpeg', '.jpg'
        elif document_bytes.startswith(b'\x89PNG\r\n\x1a\n'):
            return 'png', '.png'
        elif document_bytes.startswith(b'PK\x03\x04') and b'word/' in document_bytes[:1024]:
            return 'docx', '.docx'
        elif document_bytes.startswith(b'PK\x03\x04'):
            return 'zip', '.zip'
        elif document_bytes.startswith(b'GIF87a') or document_bytes.startswith(b'GIF89a'):
            return 'gif', '.gif'
        elif document_bytes.startswith(b'RIFF') and b'WEBP' in document_bytes[:12]:
            return 'webp', '.webp'

        logger.warning("Unknown file type detected from magic bytes")
        return 'unknown', '.bin'

    def _create_temp_file(self, content: bytes, suffix: str) -> Path:
        tmp = tempfile.NamedTemporaryFile(delete=False, suffix=suffix)
        tmp.write(content)
        tmp.flush()
        tmp.close()
        return Path(tmp.name)

    def _clean_temp_file(self, file_path: Path):
        try:
            if file_path and file_path.exists():
                os.remove(file_path)
                logger.debug(f"Deleted temp file: {file_path}")
        except Exception as err:
            logger.warning(f"Failed to delete temp file {file_path}: {err}")

    def extract_field_values(
        self,
        document: bytes,
        fields_to_extract: List[str],
        schema_type: Optional[Type] = None
    ) -> Dict[str, str]:
        prompt = extract_metadata_field_prompt(fields_to_extract)
        file_type, extension = self._detect_file_type(document)
        file_path = self._create_temp_file(document, extension)

        try:
            logger.debug(f"Sending file to Gemini: {file_path} ({file_type})")
            response = self.gemini.generate_from_file(
                file_path=str(file_path),
                prompt=prompt,
                schema_type=schema_type
            )

            if schema_type and hasattr(response, "dict"):
                return response.dict()

            if isinstance(response, dict):
                return response

            if isinstance(response, list) and all(hasattr(x, "dict") for x in response):
                return {k: v for item in response for k, v in item.dict().items()}

            if isinstance(response, str):
                cleaned = re.sub(r"^```json|```$", "", response.strip(), flags=re.IGNORECASE).strip()
                parsed = json.loads(cleaned)

                if schema_type:
                    return schema_type(**parsed).dict()
                if isinstance(parsed, dict):
                    return parsed
                if isinstance(parsed, list):
                    return {k: v for item in parsed if isinstance(item, dict) for k, v in item.items()}

            logger.warning("Unexpected Gemini response type")
            return {}

        except Exception as e:
            logger.error("Field extraction failed", exc_info=True)
            return {}

        finally:
            self._clean_temp_file(file_path)

    def generate_document_name(
        self,
        document_bytes: bytes,
        field_values: Dict[str, str],
        naming_template: str
    ) -> str:
        try:
            field_values_list = [f"{k}: {v}" for k, v in field_values.items() if v]
            prompt = generate_doc_name_by_convention_prompt(field_values_list, naming_template)

            file_type, extension = self._detect_file_type(document_bytes)
            file_path = self._create_temp_file(document_bytes, extension)

            try:
                doc_name = self.gemini.generate_from_file(
                    file_path=str(file_path),
                    prompt=prompt
                )

                if isinstance(doc_name, str):
                    cleaned = doc_name.strip().strip('"').strip("'")
                    logger.info(f"Generated document name: {cleaned}")
                    return cleaned

                logger.warning(f"Unexpected response type: {type(doc_name)}")
                return "Unknown Document"

            finally:
                self._clean_temp_file(file_path)

        except Exception as e:
            logger.error("Document name generation failed", exc_info=True)
            return "Unknown Document"

    def match_field_values(self, document_input: Union[str, bytes]) -> Optional[FilingMetadata]:
        """
        Matches project metadata from email/document input using Gemini.

        Args:
            document_input (str or bytes): Email text (str) or document content (bytes).

        Returns:
            Optional[FilingMetadata]: Extracted metadata or None if matching fails.
        """
        try:
            # Determine input type
            if isinstance(document_input, str):
                extension = ".txt"
                content = document_input.encode("utf-8")  # Convert string to bytes
            elif isinstance(document_input, bytes):
                extension = self._detect_file_type(document_input)[1]
                content = document_input
            else:
                raise ValueError(f"Unsupported input type: {type(document_input)}")

            file_path = self._create_temp_file(content, extension)
            logger.info(f"[MATCHING] Matching project metadata using Gemini with file type: {extension.lstrip('.')}")

            response = self.gemini.generate_from_file(
                file_path=str(file_path),
                prompt=match_project_prompt(),
                schema_type=FilingMetadata
            )

            return response

        except Exception as e:
            logger.error("[MATCHING] Project matching failed", exc_info=True)
            return None

        finally:
            if 'file_path' in locals():
                self._clean_temp_file(file_path)
