from src.filling_service.config import extractor
from src.shared.service_registry import doc_processor
from src.shared.utils.logger_config import setup_logger

logger = setup_logger(__name__)

# TODO remove company_data_destination_id = 6 -> used for testing
def name_generation(doc_id: int, extracted_field_values: dict, preview_bytes: bytes, doc_type_id: int, company_data_destination_id: int = 6) -> None:
    """
    Generates and stores a document name based on extracted field values and naming convention template.

    Args:
        doc_id (int): ID of the document.
        extracted_field_values (dict): Extracted metadata fields used for naming.
        preview_bytes (bytes): Document content used in name generation prompt.
        doc_type_id (int): The document type identifier.
        company_data_destination_id (int, optional): Destination context for naming template. Defaults to None.
    """
    service_id = 4
    logger.info(f"[NAMING] Starting document name generation for document ID: {doc_id}")
    doc_processor.log_document_service_status(doc_id, service_id=service_id, status_id=2)  # Status: Processing

    try:
        # Retrieve naming template
        naming_template = doc_processor.get_naming_template_by_document_type_and_destination(
            document_type_id=doc_type_id,
            company_data_destination_id=company_data_destination_id
        )

        if not naming_template:
            logger.warning(f"[NAMING] No naming template found for document type ID {doc_type_id} and destination ID {company_data_destination_id}")
            return

        if not extracted_field_values:
            logger.warning(f"[NAMING] No extracted values available for document ID {doc_id}, skipping name generation.")
            return

        logger.info(f"[NAMING] Using naming template: {naming_template}")

        # Generate name
        doc_name = extractor.generate_document_name(preview_bytes, extracted_field_values, naming_template)

        # Log result
        doc_processor.log_document_name(doc_id, doc_name)
        logger.info(f"[NAMING] Successfully generated and stored document name: {doc_name}")

    except Exception as naming_error:
        logger.error(f"[NAMING] Error during name generation for document ID {doc_id}: {naming_error}", exc_info=True)

# Sample Usage
# with open("/Users/<USER>/Desktop/fileflow/fileflow.document-classifier/tests_classifier_datasets/non_court_docs/non_court_documents_dataset/1p UM_UIM LOR - State Farm - Angelica de Lacerda.pdf", "rb") as f:
#     preview_bytes = f.read()
# extracted_fields = {"Defendant Name": "Maria Guzman, Francisco Guzman, and John Doe"}
# document_name = name_generation(doc_id=268, extracted_field_values=extracted_fields, preview_bytes=preview_bytes, doc_type_id=101, company_data_destination_id=6)
# print(document_name)
