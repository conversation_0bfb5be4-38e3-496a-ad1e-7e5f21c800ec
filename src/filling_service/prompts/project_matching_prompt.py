def match_project_prompt() -> str:

    prompt = f"""
You are an attentive and careful information extraction system. Your task is to read the provided email text and extract specific pieces of information into a structured format.

**Instructions:**
1.  Read the text provided between the `---EMAIL TEXT---` markers carefully.
2.  Extract values for *only* the fields listed below.
3.  If a field is explicitly mentioned in the text, extract its value.
4.  If a field is *not* found in the text or is not applicable, return the string "N/A" for that field's value.
5.  Pay close attention to the specific extraction rules provided for each field below.
6.  Output the extracted information as a JSON object. The JSON keys *must* exactly match the field names listed below.

**Fields to Extract (JSON Keys):**

*   `Project ID`: Look for the "Case Number". If "Case Number" is not present, look for "Envelope Number". Return the found number or "N/A".
*   `Incident Date`: Look for the date associated with "Date/Time Submitted" or "Date Submitted". Extract only the date part (ignore time and timezone). Format as YYYY-MM-DD if possible, otherwise use the original date format found. If no date is found, return "N/A".
*   `Incident Type`: Look for the "Filing Type". Return the found value or "N/A".
*   `Lead Attorney`: Look for a person's name listed under "Service Contacts" or associated with "Filed By". Prioritize a name associated with the filing party (e.g., JDB Lawfirm in this text). Return the most likely lead attorney name found, or "N/A" if none is clearly identifiable as a lead attorney from the text.
*   `First Primary`: Look for the "Case Style". Extract the name of the first party listed *before* the "v". Return the extracted name or "N/A".
*   `Project Number`: This is the same as `Project ID`. Look for the "Case Number". If "Case Number" is not present, look for "Envelope Number". Return the found number or "N/A".
*   `SOL (date only)`: This field is **not** expected to be in this type of email. Always return "N/A".
*   `Meds Total Balance Due (currency)`: This field is **not** expected to be in this type of email. Always return "N/A".
*   `Phase Name`: This field is **not** expected to be in this type of email. Always return "N/A".
*   `project email address`: Look for email addresses listed under "Service Contacts". Return the first relevant email address found (e.g., one associated with the filing party's firm), or "N/A" if no relevant email is found.
*   `project name or client name or both`: Look for the "Case Style" (which contains party names) and the "Filed By" value (the firm name). Return the "Case Style" value and/or the "Filed By" value, or "N/A" if neither is found. You can combine them or list them separately if found.

"""
    return prompt.strip()


