from pydantic import BaseModel, Field, EmailStr
from typing import Optional

class FilingMetadata(BaseModel):
    project_id: str = Field(default="N/A", alias="Project ID")
    incident_date: str = Field(default="N/A", alias="Incident Date")  # Format: YYYY-MM-DD if possible
    incident_type: str = Field(default="N/A", alias="Incident Type")
    lead_attorney: str = Field(default="N/A", alias="Lead Attorney")
    first_primary: str = Field(default="N/A", alias="First Primary")
    project_number: str = Field(default="N/A", alias="Project Number")  # Same as project_id
    sol_date_only: str = Field(default="N/A", alias="SOL (date only)")
    meds_total_balance_due: str = Field(default="N/A", alias="Meds Total Balance Due (currency)")
    phase_name: str = Field(default="N/A", alias="Phase Name")
    project_email_address: str = Field(default="N/A", alias="project email address")
    project_or_client_name: str = Field(default="N/A", alias="project name or client name or both")
