from src.filling_service.config import extractor
from src.filling_service.project_matching.fetch_email_info import get_email_info
from src.shared.service_registry import doc_processor
from src.shared.utils.logger_config import setup_logger

logger = setup_logger(__name__)

def match_project(doc_id: int):

    email_info = get_email_info(doc_id)
    print(f"Type of email_info: {type(email_info)}")

    matched_fields = extractor.match_field_values(email_info)
    logger.info(f"Matched fields: {matched_fields}")
    if matched_fields:
        match_fields = matched_fields[0]
        email_address = match_fields.project_email_address
    else:
        logger.warning("No matched metadata available.")

    logger.info(f"Email Address: {email_address}")

    company_data_destination_id = 6

    mismatches = doc_processor.find_matching_project_by_email("<EMAIL>", company_data_destination_id=6)

    if match found:
        get field values for that project and compare against the field values we have

    if match not found:
        compare client name

    if natch not found:
        compare project number



        )

    if mismatches:
        logger.warning(f"Fields not matching DB: {mismatches}")
    else:
        logger.info("All metadata fields match the Filevine project.")

    return match_fields



match_project(268)