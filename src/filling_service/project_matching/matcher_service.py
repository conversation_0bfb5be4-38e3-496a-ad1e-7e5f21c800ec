from typing import Tuple, Dict

from src.shared.utils.logger_config import setup_logger

logger = setup_logger(__name__)

class ProjectMatcher:
    def __init__(self, doc_processor):
        self.doc_processor = doc_processor

    def match_project_email(self, email_address, company_data_destination_id):

        project_id = self.doc_processor.find_matching_project_by_email(email_address,
                                                                  company_data_destination_id=6)
        return project_id

    def match_on_project_id(self, project_id, company_data_destination_id):

        project_found = self.doc_processor.find_matching_project_by_project_id(project_id,
                                                                            company_data_destination_id=6)
        return project_found

    def match_on_client_name(self, client_name, company_data_destination_id):

        project_found = self.doc_processor.find_matching_project_by_client_name(client_name,
                                                                            company_data_destination_id=6)
        return project_found

    def match_project_by_secondary_identifiers(self, metadata, company_data_destination_id):

        project_id, matches, mismatches = self.doc_processor.find_project_by_field_priority(metadata,
                                                                                            company_data_destination_id=6)
        return project_id, matches, mismatches

    # def match_project(self, metadata, company_data_destination_id):
    #     # 1. Match by Project ID
    #     if metadata.project_id:
    #         project_id = self.doc_processor.find_matching_project_by_project_id(
    #             metadata.project_id, company_data_destination_id
    #         )
    #         if project_id:
    #             logger.info(f"✅ Matched by Project ID: {project_id}")
    #             return project_id
    #
    #     # 2. Match by Project Email
    #     if metadata.email_address:
    #         email_project_id = self.doc_processor.find_matching_project_by_email(
    #             metadata.email_address, company_data_destination_id
    #         )
    #         if email_project_id:
    #             logger.info(f"✅ Matched by Project Email: {email_project_id}")
    #
    #             # Try enhancing match with client name
    #             if metadata.client_name:
    #                 name_project_id = self.doc_processor.find_matching_project_by_client_name(
    #                     metadata.client_name, company_data_destination_id
    #                 )
    #                 if name_project_id:
    #                     matches, mismatches = self.doc_processor.compare_metadata_fields_with_project(
    #                         name_project_id, metadata
    #                     )
    #                     if not mismatches:
    #                         logger.info(f"✅ Matched by Email + Client Name + Metadata: {name_project_id}")
    #                         return name_project_id
    #                     else:
    #                         logger.info(f"ℹ️ Matched by Email + Client Name (low confidence): {name_project_id}")
    #                         return name_project_id
    #             return email_project_id
    #
    #     # 3. Match by Client Name
    #     if metadata.client_name:
    #         name_project_id = self.doc_processor.find_matching_project_by_client_name(
    #             metadata.client_name, company_data_destination_id
    #         )
    #         if name_project_id:
    #             matches, mismatches = self.doc_processor.compare_metadata_fields_with_project(
    #                 name_project_id, metadata
    #             )
    #             if not mismatches:
    #                 logger.info(f"✅ Matched by Client Name + Metadata: {name_project_id}")
    #                 return name_project_id
    #             else:
    #                 logger.info(f"ℹ️ Matched by Client Name (metadata mismatch): {name_project_id}")
    #                 return name_project_id
    #
    #     # 4. Match by secondary metadata fields
    #     project_ids, matches, mismatches = self.doc_processor.find_project_by_field_priority(
    #         metadata, company_data_destination_id
    #     )
    #     if project_ids:
    #         logger.info(f"✅ Matched by secondary metadata: {project_ids}")
    #         return project_ids[0] if len(project_ids) == 1 else project_ids
    #
    #     # No match
    #     logger.warning("❌ No matching project found.")
    #     return None
    def verify_secondary_metadata(
            self,
            project_id,
            metadata,
            company_data_destination_id,
            check_email: bool = True,
            check_client_name: bool = True
    ) -> Dict[str, Tuple[str, str]]:
        """
        Verifies additional metadata fields (client name, email, and others) for a matched project.
        Returns only matched fields.
        """

        matches: Dict[str, Tuple[str, str]] = {}

        # 1. Check client name
        if check_client_name:
            client_name = getattr(metadata, "project_or_client_name", None)
            if client_name:
                matched_id = self.doc_processor.find_matching_project_by_client_name(
                    client_name, company_data_destination_id
                )
                if matched_id == project_id:
                    matches["project_or_client_name"] = (client_name, client_name)

        # 2. Check project email address
        if check_email:
            email = getattr(metadata, "project_email_address", None)
            if email:
                matched_id = self.doc_processor.find_matching_project_by_email(
                    email, company_data_destination_id
                )
                if matched_id == project_id:
                    matches["project_email_address"] = (email, email)

        # 3. Compare other metadata fields
        extra_matches, _ = self.doc_processor.compare_metadata_fields_with_project(
            filevine_project_id=project_id,
            metadata=metadata
        )

        # Merge all matched fields
        matches.update(extra_matches)

        return matches

    def match_project(self, metadata, company_data_destination_id):
        # 1. Try Project ID
        if metadata.project_id:
            project_id = self.doc_processor.find_matching_project_by_project_id(
                metadata.project_id, company_data_destination_id
            )
            if project_id:
                logger.info(f"Matched by Project ID: {project_id}")
                matches = self.verify_secondary_metadata(
                    project_id, metadata, company_data_destination_id
                )
                return project_id, matches

        # 2. Try Project Email
        email = getattr(metadata, "project_email_address", None)
        if email:
            email_project_id = self.doc_processor.find_matching_project_by_email(
                email, company_data_destination_id
            )
            if email_project_id:
                logger.info(f"✅ Matched by Email: {email_project_id}")
                matches = self.verify_secondary_metadata(
                    email_project_id, metadata, company_data_destination_id, check_email=False
                )
                return email_project_id, matches

        # 3. Try Client Name
        client_name = getattr(metadata, "project_or_client_name", None)
        if client_name:
            name_project_id = self.doc_processor.find_matching_project_by_client_name(
                client_name, company_data_destination_id
            )
            if name_project_id:
                logger.info(f"✅ Matched by Client Name: {name_project_id}")
                matches = self.verify_secondary_metadata(
                    name_project_id, metadata, company_data_destination_id, check_email=False, check_client_name=False
                )
                return name_project_id, matches

        # 4. Try fallback secondary metadata fields
        project_ids, matches, mismatches = self.doc_processor.find_project_by_field_priority(
            metadata, company_data_destination_id,
        )
        if project_ids:
            logger.info(f"✅ Matched by secondary metadata: {project_ids}")
            return project_ids[0] if len(project_ids) == 1 else project_ids

        # No match
        logger.warning("❌ No matching project found.")
        return None

