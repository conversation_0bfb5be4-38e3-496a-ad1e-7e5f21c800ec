from src.classifier_service.common.classifier_router import route_classifier_by_data_source
from src.classifier_service.common.graph_state import DocumentState
from src.classifier_service.common.utils.clean_storage_name import extract_blob_name
from src.classifier_service.common.utils.service_status_logging import _log_failure
from src.shared.service_registry import doc_processor, blob_client
from src.shared.utils.logger_config import setup_logger

logger = setup_logger(__name__)


def execute_classification_pipeline(doc_id: int) -> None:
    """
    Executes the rule-based classification pipeline for a given document ID.

    This includes fetching metadata, loading the document, routing to the appropriate
    classifier, storing the classification result, and updating the document status.

    Args:
        doc_id (int): ID of the document to classify.

    Raises:
        ValueError: If any step fails (e.g., missing document, failed classification).
    """
    service_id = 2  # document_classifier
    try:
        logger.info(f"---- STARTING CLASSIFICATION FOR DOCUMENT ID: {doc_id} ----")
        doc_processor.log_document_service_status(doc_id, service_id=service_id, status_id=2)  # processing

        # Step 1: Fetch document metadata
        email_info = doc_processor.get_document_info(doc_id)
        if not email_info:
            _log_failure(doc_id, service_id, "Document metadata not found.")
            return

        document_location = email_info.get("storage_location")
        title = email_info.get("original_name")
        email_source = email_info.get("metadata", {}).get("source", "unknown")

        if not document_location or not title:
            _log_failure(doc_id, service_id, "Missing document storage location or title.")
            return

        logger.info(f"Document Location: {document_location}")
        logger.info(f"Email Source: {email_source}")
        logger.info(f"Document Title: {title}")

        # Step 2: Determine blob location
        company_id = doc_processor.get_company_id_by_document_id(doc_id)
        if not company_id:
            _log_failure(doc_id, service_id, "Company ID not found.")
            return

        container_name = f"company-{company_id}"
        blob_name = extract_blob_name(document_location)

        logger.info(f"Company ID: {company_id}")
        logger.info(f"Container: {container_name}, Blob: {blob_name}")

        # Step 3: Fetch document preview
        doc = blob_client.get_document_preview(container_name, blob_name, num_pages=5)
        if not doc:
            _log_failure(doc_id, service_id, f"Unable to fetch document from blob storage: {document_location}")
            return

        logger.info("Fetched document from blob storage.")

        # Step 4: Run classification
        classifier = route_classifier_by_data_source(email_source)
        initial_state = DocumentState(
            doc_title=title,
            document=doc,
            classification_result=None
        )

        final_state = classifier.invoke(initial_state)
        classification = final_state.get("classification_result")

        if not classification:
            _log_failure(doc_id, service_id, "Document classification returned no result.")
            return

        logger.info(f"Document classified as: {classification}")

        # Step 5: Persist classification result
        doc_type_id = doc_processor.get_document_type_id_by_doc_type_name(classification)
        if not doc_type_id:
            _log_failure(doc_id, service_id, f"No document type ID found for classification: {classification}")
            return

        doc_processor.log_document_type_id(doc_id, doc_type_id)
        logger.info(f"Document Type ID {doc_type_id} logged successfully.")

        # Step 6: Mark service as successful
        doc_processor.log_document_service_status(doc_id, service_id=service_id, status_id=3)
        logger.info(f"---- CLASSIFICATION COMPLETED FOR DOCUMENT ID: {doc_id} ----")

    except Exception as e:
        logger.exception(f"Unexpected error during classification for document ID {doc_id}: {e}")
        _log_failure(doc_id, service_id, f"Unexpected error: {e}")
        raise

# Sample Usage
doc = execute_classification_pipeline(27)
