from enum import Enum

class GeneralCommunicationAndAdministrativeDocuments(Enum):
    """
    Enum representing all document types under the General Communication & Administrative category.
    """
    # General Communication
    CHANGE_OF_ADDRESS_LETTER = "Change of Address Letter"
    CORRESPONDENCE = "Correspondence"
    DROP_LETTER = "Drop Letter"
    EMAIL = "Email"
    FAX_CONFIRMATION = "Fax Confirmation"
    LETTER = "Letter"
    MEMORANDUM = "Memorandum"

    # Administrative / Proof-related
    CERTIFIED_MAIL = "Certified Mail"
    CHANGE_OF_ADDRESS_VERIFICATION = "Change of Address Verification"
    GREEN_RECEIPT = "Green Receipt"
    PRIVACY_NOTICE = "Privacy Notice"
    PROOF_OF_SERVICE = "Proof of Service"
    FORM_OR_QUESTIONNAIRE = "Form / Questionnaire"
    ERRATA_PAGE = "Errata Page"