from enum import Enum

class GovernmentAndAgencyDocuments(Enum):
    """
    Enum representing all document types under the Government & Agency classification.
    """

    # Federal Benefits & Tax
    ANNUAL_SSA_BENEFIT_STATEMENT = "Annual SSA Benefit Statement"
    ECONOMIC_STIMULUS_DOCUMENTATION_SSA = "Economic Stimulus Documentation SSA"
    IRS_LETTER = "IRS Letter"
    SSA_ACCOUNT_STATEMENT = "SSA Account Statement"
    SSA_WITHHOLDING_NOTICE = "SSA Withholding Notice"
    TAX_DOCUMENT = "Tax Document"

    # State & Local Records and Identification
    ANIMAL_CONTROL_REPORT = "Animal Control Report"
    CRASH_REPORT_COST = "Crash Report Cost"
    CRIMINAL_BACKGROUND = "Criminal Background"
    LETTER_TO_SOS = "Letter to SOS"
    POLICE_OFFICE_CASE_REPORT = "Police Office Case Report"
    POLICE_REPORT = "Police Report"
    BIRTH_CERTIFICATE = "Birth Certificate"
    DRIVER_LICENSE = "Driver License"
    PROOF_OF_ADDRESS = "Proof of Address"
    VEHICLE_TITLE = "Vehicle Title"
    VERIFICATION_OF_ADDRESS = "Verification of Address"
    TAX_FORM = "Tax Form"