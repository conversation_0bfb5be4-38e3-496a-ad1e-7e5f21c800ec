from enum import Enum

class HealthMedicalSubType(Enum):
    """
    Enum representing the subcategories under Health & Medical classification.
    """
    GOVERNMENT_BENEFIT_ADMINISTRATION = "Government Benefit Administration"
    MEDICAL_RECORDS_AND_EVALUATIONS = "Medical Records & Evaluations"
    HEALTHCARE_FINANCE_AND_CLAIMS = "Healthcare Finance, Coverage & Government Claims"


class GovernmentBenefitAdministration(Enum):
    """Documents related to SSA/VA applications, confirmations, and benefit reports."""

    APPLICATION_FOR_BENEFITS = "Application for Benefits"
    BLANK_DISABILITY_REPORT = "Blank Disability Report"
    BURIAL_BENEFITS = "Burial Benefits"
    SSA_BENEFIT_CONFIRMATION_LETTER = "SSA Benefit Confirmation Letter"
    SSA_DISABILITY_REPORT = "SSA Disability Report"
    SSA_GENERAL_DOCS = "SSA General Docs"
    SSA_REP_PAYEE_REPORT = "SSA Rep Payee Report"
    SSA_WITHHOLDING_NOTICE = "SSA Withholding Notice"

class MedicalRecordsAndEvaluations(Enum):
    """Documents containing patient medical history, evaluations, and health assessments."""

    BEHAVIOR_CHART = "Behavior Chart"
    IMMUNIZATION_REPORT = "Immunization Report"
    MED_LOG = "Med Log"
    MEDICAL_RECORDS = "Medical Records"
    PRESCRIPTION_MEDICATION_RECORD = "Prescription / Medication Record"
    EXPLANATION_OF_REVIEW = "Explanation of Review"
    IME_REPORT = "IME Report"
    MEDICATION_THERAPY_MANAGEMENT_REPORT = "Medication Therapy Management Report (MTM)"
    MEDICAL_DISABILITY_NOTE = "Medical Disabilty Note"
    MEDICAL_EXAMINATION_LETTER = "Medical Examination Letter"
    PSYCH_REPORT = "Psych Report"
    MEDICAL_RECORD_REQUEST = "Medical Record Request"
    FMLA_LETTER = "FMLA Letter" # new


class HealthcareFinanceAndClaims(Enum):
    """Documents related to billing, coverage, government claims, and medical financial records."""

    MEDICAL_EQUIPMENT_BILL = "Medical Equipment Bill"
    MEDICAL_EQUIPMENT_REPAIR_PRESCRIPTION = "Medical Equipment Repair Prescription"
    MEDICAL_EQUIPMENT_REPAIR_QUOTE = "Medical Equipment Repair Quote"
    MEDICAL_ESTIMATE = "Medical Estimate"
    REDUCTION_APPROVAL = "Reduction Approval"
    REDUCTION_LETTER = "Reduction Letter"
    REDUCTION_REQUEST_LETTER = "Reduction Request Letter"
    HEALTH_COVERAGE_APPLICATION = "Health Coverage Application"
    HEALTHCARE_COVERAGE_DETERMINATION = "Healthcare Coverage Determination"
    MED_AUTHORIZATION_REQ = "Med Authorization Req"
    MEDICAL_SERVICE_AUTHORIZATION = "Medical Service Authorization"
    PRE_SERVICE_COVERAGE_DETERMINATION = "Pre-Service Coverage Determination"
    SPECIALIST_REFERRAL_REQUIREMENT_NOTICE = "Specialist Referral Requirement Notice"
    CMS_BENEFICIARY_CONDITIONAL_PAYMENT_LETTER = "CMS Beneficiary Conditional Payment Letter"
    CMS_DEBT_COLLECTION_NOTICE = "CMS Debt Collection Notice"
    CMS_PAYMENT_CLAIM_DISPUTE = "CMS Payment Claim Dispute"
    CMS_PAYMENT_CONFIRMATION = "CMS Payment Confirmation"
    CMS_REPAYMENT_TO_MEDICARE = "CMS Repayment to Medicare"
    MEDICAID = "Medicaid"
    MEDICARE = "Medicare"
    MEDICARE_MEDICAID_SUMMARY = "Medicare / Medicaid Summary"
    MEDICARE_ATTORNEY_CASE_INFORMATION = "Medicare Attorney Case Information"
    MEDICARE_CORRESPONDENCE = "Medicare Correspondence"
    MEDICARE_INFO_REQUEST = "Medicare Info Request"
    MEDICARE_LIEN = "Medicare Lien"
    MEDICARE_PAPERWORK = "Medicare Paperwork"
    MEDICARE_RECOVERY_PROCESS = "Medicare Recovery Process"
    MEDICARE_SERVICE_NOTICE = "Medicare Service Notice"
    MEDICARE_SUMMARY = "Medicare Summary"
    SSA_TO_MEDICARE_PAYMENT_NOTICE = "SSA to Medicare Payment Notice"