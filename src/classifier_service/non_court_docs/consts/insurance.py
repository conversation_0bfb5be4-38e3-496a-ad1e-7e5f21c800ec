from enum import Enum


class InsuranceSubType(Enum):

    AUTO_INSURANCE_CARD = "Auto Insurance Card"
    AUTO_INSURANCE_PIP_COVERAGE_UPDATE = "Auto Insurance PIP Coverage Update"
    CONFIRMATION_OF_COVERAGE = "Confirmation of Coverage"
    DECLARATION_PAGE = "Declaration Page"
    EXPLANATION_OF_BENEFITS = "Explanation of Benefits"
    INSURANCE_CARD = "Insurance Card"
    INSURANCE_LETTER = "Insurance Letter"
    INSURANCE_POLICY = "Insurance Policy"
    INSURANCE_OR_ID_CARD = "Insurance or ID Card"
    POLICY = "Policy"
    SUMMARY_PLAN_DESCRIPTION = "Summary Plan Description"
    CLAIM_DOCS = "Claim Docs"
    CLAIM_ESTIMATE_OR_APPRAISAL = "Claim Estimate / Appraisal"
    COVERAGE_DENIAL = "Coverage Denial"
    DENIAL_OF_LIABILITY = "Denial of Liability"
    DRIVERS_EXCHANGE = "Drivers Exchange"
    HEALTH_INSURANCE_CLAIM_FORM = "Health Insurance Claim Form"
    LETTER_TO_CARRIER = "Letter to Carrier"
    LIABILITY_DENIAL = "Liability Denial"
    MEDPAY_DISPUTE_LETTER = "Medpay Dispute Letter"
    MENTAL_HEALTH_CLAIM_APPROVAL_NOTICE = "Mental Health Claim Approval Notice"
    REQUEST_FOR_CLAIM_INFO = "Request for Claim Info"
    TOTAL_LOSS_REPORT = "Total Loss Report"
    UM_REJECTION = "UM Rejection"
    UM_FORM = "UM form"
    WAIVING_SUBROGATION_RIGHTS = "Waiving Subrogation Rights"
    HOMEOWNERS_INSURANCE_INVOICE = "Homeowners Insurance Invoice"
    PIP_LOG = "PIP Log"
    PIP_EXHAUSTED = "PIP Exhausted" # new
    PIP_SUSPENDED = "PIP Suspended" # new



# class InsuranceSubType(Enum):
#     """
#     Enum representing the two main insurance subcategories.
#     """
#     POLICY_DETAILS_AND_COVERAGE = "Policy Details & Coverage"
#     CLAIMS_PROCESS_AND_COMMUNICATION = "Claims Process & Communication"
#
#
# class PolicyDetailsAndCoverage(Enum):
#     """Documents related to insurance policy coverage and benefits."""
#
#     AUTO_INSURANCE_CARD = "Auto Insurance Card"
#     AUTO_INSURANCE_PIP_COVERAGE_UPDATE = "Auto Insurance PIP Coverage Update"
#     CONFIRMATION_OF_COVERAGE = "Confirmation of Coverage"
#     DECLARATION_PAGE = "Declaration Page"
#     EXPLANATION_OF_BENEFITS = "Explanation of Benefits"
#     INSURANCE_CARD = "Insurance Card"
#     INSURANCE_LETTER = "Insurance Letter"
#     INSURANCE_POLICY = "Insurance Policy"
#     INSURANCE_OR_ID_CARD = "Insurance or ID Card"
#     POLICY = "Policy"
#     SUMMARY_PLAN_DESCRIPTION = "Summary Plan Description"
#
#
# class ClaimsProcessAndCommunication(Enum):
#     """Documents related to insurance claims and communication during claims handling."""
#
#     # CLAIM_DOCS = "Claim Docs"
#     CLAIM_ESTIMATE_OR_APPRAISAL = "Claim Estimate / Appraisal"
#     COVERAGE_DENIAL = "Coverage Denial"
#     DENIAL_OF_LIABILITY = "Denial of Liability"
#     DRIVERS_EXCHANGE = "Drivers Exchange"
#     HEALTH_INSURANCE_CLAIM_FORM = "Health Insurance Claim Form"
#     LETTER_TO_CARRIER = "Letter to Carrier"
#     LIABILITY_DENIAL = "Liability Denial"
#     MEDPAY_DISPUTE_LETTER = "Medpay Dispute Letter"
#     MENTAL_HEALTH_CLAIM_APPROVAL_NOTICE = "Mental Health Claim Approval Notice"
#     REQUEST_FOR_CLAIM_INFO = "Request for Claim Info"
#     TOTAL_LOSS_REPORT = "Total Loss Report"
#     UM_REJECTION = "UM Rejection"
#     UM_FORM = "UM form"
#     WAIVING_SUBROGATION_RIGHTS = "Waiving Subrogation Rights"
#     HOMEOWNERS_INSURANCE_INVOICE = "Homeowners Insurance Invoice"
#     PIP_LOG = "PIP Log"
#     PIP_EXHAUSTED = "PIP Exhausted" # new
#     PIP_SUSPENDED = "PIP Suspended" # new
#