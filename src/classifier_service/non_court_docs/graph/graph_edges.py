from typing import Dict
from langgraph.graph import StateGraph
from src.classifier_service.non_court_docs.consts.case_claim import CaseClaimSubType
from src.classifier_service.non_court_docs.consts.financial import FinancialTypes
from src.classifier_service.non_court_docs.consts.general_classification import GeneralTypeClassification
from src.classifier_service.non_court_docs.consts.insurance import InsuranceSubType
from src.classifier_service.non_court_docs.consts.medical import HealthMedicalSubType
from src.shared.utils.logger_config import setup_logger

logger = setup_logger(__name__)

def rule_based_router(state: Dict) -> str:
    """
    Route based on high-level rule-based classification result.
    """
    classification = state.get("classification_result", "")
    if not isinstance(classification, str):
        logger.warning("[ROUTER] Missing or invalid classification_result")
        return "self_correction"

    classification = classification.lower()

    if classification == "other_classification":
        logger.info("[ROUTER] No rule-based classification matched, routing to general_type_classification.")
        return "general_type_classification"
    elif classification == "notice":
        return "notice_classifier"
    elif classification == "bill":
        return "self_correction"
    else:
        logger.info(f"[ROUTER] Rule-based classification matched: {classification} — ending graph.")
        return "__end__"


def route_document_by_general_type(state: Dict) -> str:
    general_type_str = state.get("classification_result", "")
    if not isinstance(general_type_str, str):
        return "self_correction"

    try:
        general_type = GeneralTypeClassification(general_type_str)
    except ValueError:
        logger.warning(f"[ROUTER] Invalid general type: {general_type_str}")
        return "self_correction"

    logger.info(f"[ROUTER] classification_result: {general_type}")

    match general_type:
        case GeneralTypeClassification.FINANCIAL_INVESTMENT:
            return "financial_documents_classification"
        case GeneralTypeClassification.INSURANCE:
            return "insurance_documents_classification"
        case GeneralTypeClassification.HEALTH_MEDICAL:
            return "medical_type_classifier"
        case GeneralTypeClassification.GOVERNMENT_AGENCY:
            return "government_agency_classifier"
        case GeneralTypeClassification.CASE_CLAIM_MANAGEMENT:
            return "case_claim_management_classifier"
        case GeneralTypeClassification.GENERAL_COMMUNICATION_ADMINISTRATIVE:
            return "general_correspondence_classifier"
        case _:
            return "self_correction"


def financial_type_router(state: Dict) -> str:
    financial_type = state.get("classification_result", "")
    if not isinstance(financial_type, str):
        return "self_correction"

    match financial_type:
        case FinancialTypes.BANKING_AND_ACCOUNTS.value:
            return "banking_and_accounting_doc_type"
        case FinancialTypes.LOANS_AND_CREDIT.value:
            return "loans_and_credits_doc_type"
        case FinancialTypes.INVESTMENTS_AND_RETIREMENT.value:
            return "investment_and_retirement_doc_type"
        case FinancialTypes.INCOME_AND_BENEFITS.value:
            return "income_and_benefits_doc_type"
        case FinancialTypes.BILLING_INVOICES_PAYMENTS.value:
            return "billing_invoices_payments_doc_type"
        case FinancialTypes.PROPERTY_AND_ASSET_MANAGEMENT.value:
            return "property_and_asset_management_doc_type"
        case _:
            return "self_correction"


def medical_type_router(state: Dict) -> str:
    medical_type = state.get("classification_result", "")
    if not isinstance(medical_type, str):
        return "self_correction"

    match medical_type:
        case HealthMedicalSubType.GOVERNMENT_BENEFIT_ADMINISTRATION.value:
            return "gov_benefit_administration_classifier"
        case HealthMedicalSubType.MEDICAL_RECORDS_AND_EVALUATIONS.value:
            return "medical_records_evaluation_classifier"
        case HealthMedicalSubType.HEALTHCARE_FINANCE_AND_CLAIMS.value:
            return "healthcare_finance_claims_classifier"
        case _:
            return "self_correction"


def case_management_router(state: Dict) -> str:
    case_management_type = state.get("classification_result", "")
    if not isinstance(case_management_type, str):
        return "self_correction"

    match case_management_type:
        case CaseClaimSubType.CLIENT_INTAKE_AND_PROFILE.value:
            return "client_profile_classifier"
        case CaseClaimSubType.CASE_MANAGEMENT_AND_WORKFLOW.value:
            return "case_management_classifier"
        case CaseClaimSubType.CLAIMS_AND_RESOLUTIONS.value:
            return "case_resolution_classifier"
        case CaseClaimSubType.FORMAL_LEGAL_AND_ADMIN.value:
            return "formal_legal_admin_classifier"
        case _:
            return "self_correction"


def add_final_node_routing_to_correction(builder: StateGraph, final_nodes: list[str]) -> None:
    """
    Adds a routing edge from each final node to the 'self_correction' node.

    Args:
        builder: The StateGraph builder instance.
        final_nodes: List of node names that are leaf nodes (typically routed to __end__).
    """
    for node_name in final_nodes:
        builder.add_conditional_edges(node_name, lambda state: "self_correction")


# TODO - test combining in one node
# def insurance_type_router(state) -> str:
#     insurance_type = state["classification_result"]
#
#     if insurance_type == InsuranceSubType.POLICY_DETAILS_AND_COVERAGE.value:
#         return "policy_details_and_coverage_type"
#     elif insurance_type == InsuranceSubType.CLAIMS_PROCESS_AND_COMMUNICATION.value:
#         return "claims_process_and_communication_type"
#     else:
#         return "unknown_insurance_type_handler"