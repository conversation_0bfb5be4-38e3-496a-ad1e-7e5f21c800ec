from src.classifier_service.common.prompt_generator import generate_category_prompt, generate_enhanced_category_prompt
from src.classifier_service.non_court_docs.consts.case_claim import CaseClaimSubType, ClientIntakeAndProfile, \
    CaseManagementAndWorkflow, ClaimsResolutionsAndAgreements, FormalLegalAndSpecializedAdmin
from src.classifier_service.non_court_docs.consts.category_descriptions import financial_type_descriptions, \
    general_type_descriptions, billing_docs_descriptions, insurance_subtype_descriptions, \
    health_medical_subtype_descriptions, case_claim_subtype_descriptions
from src.classifier_service.non_court_docs.consts.financial import FinancialTypes, BankingAndAccounts, LoansAndCredit, \
    InvestmentsAndRetirement, IncomeAndBenefitsFinancial, BillingInvoicesPayments, PropertyAndAssetManagement
from src.classifier_service.non_court_docs.consts.general_classification import GeneralTypeClassification
from src.classifier_service.non_court_docs.consts.general_correspondance import \
    GeneralCommunicationAndAdministrativeDocuments
from src.classifier_service.non_court_docs.consts.government import GovernmentAndAgencyDocuments
from src.classifier_service.non_court_docs.consts.insurance import InsuranceSubType
from src.classifier_service.non_court_docs.consts.medical import HealthMedicalSubType, GovernmentBenefitAdministration, \
    MedicalRecordsAndEvaluations, HealthcareFinanceAndClaims
from src.classifier_service.non_court_docs.consts.notices import NoticeDocuments
from src.classifier_service.non_court_docs.glossary.financial.banking_accounts_definitions import \
    BANKING_ACCOUNTS_DEFINITIONS
from src.classifier_service.non_court_docs.prompts.base_prompts import base_non_court_classification_prompt

# General Document Classification Prompts
general_type_classification_prompt = generate_category_prompt(base_non_court_classification_prompt, GeneralTypeClassification, "General Document Types", general_type_descriptions)
# print(general_type_classification_prompt)

# Financial and Banking Document Classification Prompts
# banking_and_accounting_classification_prompt = generate_enhanced_category_prompt(
#     base_non_court_classification_prompt,
#     BANKING_ACCOUNTS_DEFINITIONS,
#     "Banking and Accounting Document Types",
#     include_definitions=True,
#     include_usage_notes=True
# )
# print(banking_and_accounting_classification_prompt)

financial_type_classification_prompt = generate_category_prompt(base_non_court_classification_prompt, FinancialTypes, "Financial Document Types", financial_type_descriptions)
# print(f"Financial Type Classification Prompt: \n\n {financial_type_classification_prompt}")
banking_and_accounting_classification_prompt = generate_category_prompt(base_non_court_classification_prompt, BankingAndAccounts, "Banking and Accounting Document Types")
loan_and_credits_type_classification_prompt = generate_category_prompt(base_non_court_classification_prompt, LoansAndCredit, "Loans and Credit Document Types")
investment_and_retirement_classification_prompt = generate_category_prompt(base_non_court_classification_prompt, InvestmentsAndRetirement, "Investment and Retirement Document Types")
income_nad_benefits_classification_prompt = generate_category_prompt(base_non_court_classification_prompt, IncomeAndBenefitsFinancial, "Income and Benefits Document Types")
billing_invoices_payments_classification_prompt = generate_category_prompt(base_non_court_classification_prompt, BillingInvoicesPayments, "Billing, Invoices and Payments Document Types",billing_docs_descriptions)
property_and_asset_management_classification_prompt = generate_category_prompt(base_non_court_classification_prompt, PropertyAndAssetManagement, "Property and Asset Management Document Types")

# Insurance
insurance_type_classification_prompt = generate_category_prompt(base_non_court_classification_prompt, InsuranceSubType, "Insurance Document Types", insurance_subtype_descriptions)
# print(f"Insurance Type Classification Prompt: \n\n {insurance_type_classification_prompt}")
# policy_details_and_coverage_classification_prompt = generate_category_prompt(base_non_court_classification_prompt, PolicyDetailsAndCoverage, "Policy Details and Coverage Document Types")
# claims_and_communication = generate_category_prompt(base_non_court_classification_prompt, ClaimsProcessAndCommunication, "Claims and Communication Document Types")

# Medical
medical_type_classification_prompt = generate_category_prompt(base_non_court_classification_prompt, HealthMedicalSubType, "Medical Document Types", health_medical_subtype_descriptions)
# print(f"Medical Type Classification Prompt: \n\n {medical_type_classification_prompt}")
gov_benefit_administration_classification_prompt = generate_category_prompt(base_non_court_classification_prompt,  GovernmentBenefitAdministration, "Government Benefit Administration Document Types")
medical_records_evaluation_prompt = generate_category_prompt(base_non_court_classification_prompt, MedicalRecordsAndEvaluations, "Medical Records Evaluation Document Types")
healthcare_finance_claims_prompt = generate_category_prompt(base_non_court_classification_prompt, HealthcareFinanceAndClaims, "Healthcare Finance Claims Document Types")

# Government and Administrative
government_agency_classification_prompt = generate_category_prompt(base_non_court_classification_prompt, GovernmentAndAgencyDocuments, "Government Agency Document Types")

# Case and Claim Management
case_claim_management_classification_prompt = generate_category_prompt(base_non_court_classification_prompt, CaseClaimSubType, "Case and Claim Management Document Types", case_claim_subtype_descriptions)
# print(f"Case and Claim Management Prompt: \n\n {case_claim_management_classification_prompt}")
client_profile_classification_prompt = generate_category_prompt(base_non_court_classification_prompt, ClientIntakeAndProfile, "Client Profile Document Types")
case_management_classification_prompt = generate_category_prompt(base_non_court_classification_prompt, CaseManagementAndWorkflow, "Case Management Document Types")
case_resolution_classification_prompt = generate_category_prompt(base_non_court_classification_prompt, ClaimsResolutionsAndAgreements, "Case Resolution Document Types")
formal_legal_admin_prompt = generate_category_prompt(base_non_court_classification_prompt, FormalLegalAndSpecializedAdmin, "Formal Legal and Administrative Document Types")

# General Correspondence
general_communication_admin_classification_prompt = generate_category_prompt(base_non_court_classification_prompt, GeneralCommunicationAndAdministrativeDocuments, "General Communication and Administrative Document Types")

# Notices
non_court_notice_classification_prompt = generate_category_prompt(base_non_court_classification_prompt, NoticeDocuments, "Notice Document Types")