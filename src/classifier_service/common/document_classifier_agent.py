from contextlib import suppress
from pathlib import Path
import tempfile
from typing import Union, Optional
from src.shared.utils.logger_config import setup_logger

logger = setup_logger(__name__)


class DocumentClassifier:
    UNKNOWN_RESULT = "Unknown"

    def __init__(self, gemini: object):
        """
        Initialize the classifier with a Gemini model client.
        """
        self.gemini = gemini

    def classify(
        self,
        document_input: Union[bytes, str, Path],
        prompt: str,
        original_filename: str,
        schema_type: Optional[str] = None
    ) -> str:
        """
        Classify a document using Gemini based on input type.

        Args:
            document_input: Document as bytes or file path.
            prompt: Prompt string for Gemini.
            original_filename: Original name of the file (used for extension).
            schema_type: Optional schema type for Gemini.

        Returns:
            Classification result or "Unknown" if failed.
        """
        try:
            if isinstance(document_input, bytes):
                logger.debug("[Classifier] Input is bytes — routing to _classify_from_bytes")
                return self._classify_from_bytes(document_input, prompt, original_filename, schema_type)

            if isinstance(document_input, (str, Path)):
                logger.debug("[Classifier] Input is file path — routing to _classify_from_file")
                return self._classify_from_file(Path(document_input), prompt, schema_type)

            raise TypeError("Unsupported input type for classification (expected bytes, str, or Path)")

        except Exception:
            logger.error("[Classifier] Classification failed", exc_info=True)
            return self.UNKNOWN_RESULT

    def _classify_from_file(
        self, file_path: Path, prompt: str, schema_type: Optional[str] = None
    ) -> str:
        if not file_path.exists():
            logger.error(f"[File Classify] File not found: {file_path}")
            return self.UNKNOWN_RESULT

        try:
            logger.debug(f"[File Classify] Sending file to Gemini: {file_path}")
            result = self.gemini.generate_from_file(
                prompt=prompt,
                file_path=file_path,
                schema_type=schema_type
            )
            logger.info(f"Classification result: {result}")
            return result
        except Exception:
            logger.error("[File Classify] Failed to classify file", exc_info=True)
            return self.UNKNOWN_RESULT

    def _classify_from_bytes(
        self, file_bytes: bytes, prompt: str, original_filename: str, schema_type: Optional[str] = None
    ) -> str:
        ext = Path(original_filename).suffix.lower() or ".bin"
        temp_path: Optional[Path] = None

        try:
            temp_path = self._write_temp_file(file_bytes, suffix=ext)
            logger.debug(f"[Bytes Classify] Sending file to Gemini: {original_filename} ({ext})")
            result = self.gemini.generate_from_file(
                prompt=prompt,
                file_path=temp_path,
                schema_type=schema_type
            )
            logger.info(f"Classification result: {result}")
            return result

        except Exception:
            logger.error("[Bytes Classify] Failed to classify file from bytes", exc_info=True)
            return self.UNKNOWN_RESULT

        finally:
            if temp_path and temp_path.exists():
                with suppress(Exception):
                    temp_path.unlink()
                    logger.debug(f"[Temp File] Deleted temporary file: {temp_path}")

    def _write_temp_file(self, data: bytes, suffix: str) -> Path:
        """
        Write binary data to a temporary file.

        Args:
            data: The binary data to write.
            suffix: The file extension.

        Returns:
            Path to the created file.
        """
        try:
            with tempfile.NamedTemporaryFile(delete=False, suffix=suffix) as tmp:
                tmp.write(data)
                tmp.flush()
                logger.debug(f"[Temp File] Created temporary file: {tmp.name}")
                return Path(tmp.name)
        except Exception:
            logger.error("[Temp File] Failed to create temporary file", exc_info=True)
            raise