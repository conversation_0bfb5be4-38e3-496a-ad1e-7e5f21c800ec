from src.shared.service_registry import doc_processor
from src.shared.utils.logger_config import setup_logger

logger = setup_logger(__name__)

def _log_failure(doc_id: int, service_id: int, reason: str) -> None:
    """
    Logs a classification failure and provides a clear message.

    Args:
        doc_id (int): The document ID.
        service_id (int): The service ID.
        reason (str): Description of the failure.
    """
    logger.error(f"[FAILURE] Document ID {doc_id} — {reason}")
    doc_processor.log_document_service_status(doc_id, service_id=service_id, status_id=4)