import os

def extract_blob_name(blob_path: str) -> str:
    """
    Extracts just the blob file name from a full blob path.

    Examples:
    - 'company-1/20250605_file.pdf'      → '20250605_file.pdf'
    - '1/abc/def/20250618_doc.pdf'       → '20250618_doc.pdf'
    - '20250618_onlyfile.pdf'           → '20250618_onlyfile.pdf'
    """
    return os.path.basename(blob_path)

# Sample Usage
# name = extract_blob_name("1/20250605_191747_Short Jillian - NORSM - Granville Sheriff - 04.24.2025 TO FILE.pdf")
# print(name)
