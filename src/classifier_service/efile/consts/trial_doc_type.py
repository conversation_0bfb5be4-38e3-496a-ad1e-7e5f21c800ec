from enum import Enum

class TrialDocumentType(Enum):
    JOINT_PRETRIAL_STATEMENT = "Joint Pretrial Statement"
    PRETRIAL_STATEMENT = "Pretrial Statement"
    TRIAL_BRIEF = "Trial Brief"

    EXHIBIT_LIST = "Exhibit List"
    PRE_MARKED_EXHIBITS = "Pre-Marked Exhibits"

    JURY_INSTRUCTIONS = "Jury Instructions"
    PROPOSED_JURY_VERDICT_FORM = "Proposed Jury Verdict Form"
    PROPOSED_VOIR_DIRE = "Proposed Voir Dire"
    SPECIAL_VERDICT_FORM = "Special Verdict Form"
    VERDICT_FORM = "Verdict Form"
    VOIR_DIRE_QUESTIONS = "Voir Dire Questions"

    WITNESS_LIST = "Witness List"

    PROPOSED_FINDINGS_OF_FACT_AND_CONCLUSIONS_OF_LAW = "Proposed Findings of Fact and Conclusions of Law"
    CERTIFICATE_OF_AUTHORITY_FOR_MEDIATION = "Certificate of Authority for Mediation"  
    EXPERT_DISCLOSURE = "Expert Disclosures"  # Added new type
