from enum import Enum

class DiscoveryDocumentType(Enum):
    DEFENDANTS_INITIAL_DISCLOSURES = "Defendant's Initial Disclosures"
    DEPOSITIONS = "Depositions"
    EXPERT_DISCLOSURES = "Expert Disclosures"
    INTERROGATORIES = "Interrogatories"
    REQUESTS_FOR_PRODUCTION = "Requests for Production"
    REQUESTS_FOR_ADMISSION = "Requests for Admission"
    REQUEST_FOR_COMPULSORY_MEDICAL_EXAMINATION = "Request for Compulsory Medical Examination"


class InterrogatoriesDocumentType(Enum):
    INTERROGATORIES_TO_DEFENDANT = "Interrogatories to Defendant"
    INTERROGATORIES_TO_PLAINTIFF = "Interrogatories to Plaintiff"
    ANSWERS_TO_INTERROGATORIES_TO_DEFENDANT = "Answers to Interrogatories to Defendant"
    ANSWERS_TO_INTERROGATORIES_TO_PLAINTIFF = "Answers to Interrogatories to Plaintiff"


class RequestsForProductionDocumentType(Enum):
    REQUESTS_FOR_PRODUCTION_FRCP = "Requests for Production referencing FRCP"
    REQUESTS_FOR_PRODUCTION_TO_DEFENDANT = "Requests for Production to Defendant"
    REQUESTS_FOR_PRODUCTION_TO_PLAINTIFF = "Requests for Production to Plaintiff"
    RESPONSES_TO_REQUEST_FOR_PRODUCTION_TO_DEFENDANT = "Responses to Request for Production to Defendant"
    RESPONSES_TO_REQUEST_FOR_PRODUCTION_TO_PLAINTIFF = "Responses to Request for Production to Plaintiff"


class RequestsForAdmissionDocumentType(Enum):
    REQUESTS_FOR_ADMISSIONS_TO_DEFENDANT = "Requests for Admissions to Defendant"
    REQUESTS_FOR_ADMISSIONS_TO_PLAINTIFF = "Requests for Admissions to Plaintiff"
    RESPONSES_TO_REQUEST_FOR_ADMISSIONS_TO_DEFENDANT = "Responses to Request for Admissions to Defendant"
    RESPONSES_TO_REQUEST_FOR_ADMISSIONS_TO_PLAINTIFF = "Responses to Request for Admissions to Plaintiff"