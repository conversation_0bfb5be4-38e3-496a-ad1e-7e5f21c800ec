from enum import Enum

class PleadingsDocumentType(Enum):
    COMPLAINT = "Complaint"
    CIVIL_COVER_SHEET = "Civil Cover Sheet"
    SUMMONS = "Summons"
    SUMMONS_ISSUED_BY_CLERK = "Summons Issued by Clerk"
    ANSWER = "Answer"
    AMENDED_COMPLAINT = "Amended Complaint"
    AMENDED_ANSWER = "Amended Answer"
    CERTIFICATE_OF_INTERESTED_PARTIES = "Certificate of Interested Parties"
    APPELLATE_DOCKETING_STATEMENT = "Appellate Docketing Statement"