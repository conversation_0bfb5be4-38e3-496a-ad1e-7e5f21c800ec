from langgraph.graph import StateGraph
from src.classifier_service.efile.consts.discovery_types import DiscoveryDocumentType
from src.classifier_service.efile.consts.motion_types import MotionCategory
from src.classifier_service.efile.consts.notice_type import NoticeCategory


def route_by_rule_based_classification(state: dict) -> str | None:
    result = state.get("classification_result", "").strip().lower()
    if not isinstance(result, str):
        return "self_correction"

    match result:
        case "notice":
            return "classify_general_notice_type"
        case "motion":
            return "classify_general_motion_type"
        case "discovery":
            return "classify_general_discovery_type"
        case "next":
            return "initial_legal_doc_type"
        case "order_type":
            return "classify_order_type"
        case _:
            # return "__end__"  # classification is final
            return "self_correction"


def route_by_legal_category(state: dict) -> str:
    result = state.get("classification_result", "").strip().lower()
    if not isinstance(result, str):
        return "self_correction"

    match result:
        case "pleadings & case initiation":
            return "classify_pleading_type"
        case "service of process & proof of service":
            return "classify_proof_of_service"
        case "judgments & post-trial":
            return "classify_judgment_type"
        case "affidavits & declarations (general)":
            return "classify_affidavit_type"
        case "trial & pre-trial preparations":
            return "classify_trial_type"
        case "orders & case management":
            return "classify_order_type"
        case "notices":
            return "classify_general_notice_type"
        case "motions":
            return "classify_general_motion_type"
        case "discovery":
            return "classify_general_discovery_type"
        case _ :
            # return "__end__"  # classification is final
            return "self_correction"

def route_discovery_subtype(state: dict) -> str:
    classification = state["classification_result"].strip()
    if not isinstance(classification, str):
        return "self_correction"

    if classification in [
        DiscoveryDocumentType.DEFENDANTS_INITIAL_DISCLOSURES.value,
        DiscoveryDocumentType.DEPOSITIONS.value,
        DiscoveryDocumentType.EXPERT_DISCLOSURES.value,
        DiscoveryDocumentType.REQUEST_FOR_COMPULSORY_MEDICAL_EXAMINATION.value
    ]:
        return "__end__"  # end of graph

    elif classification == DiscoveryDocumentType.INTERROGATORIES.value:
        return "classify_interrogatory_type"

    elif classification == DiscoveryDocumentType.REQUESTS_FOR_PRODUCTION.value:
        return "classify_request_for_production_type"

    elif classification == DiscoveryDocumentType.REQUESTS_FOR_ADMISSION.value:
        return "classify_request_for_admission_type"

    # return "__end__"  # default safety
    return "self_correction"


def route_motion_type(state: dict) -> str:
    motion_type = state["classification_result"].strip()
    if not isinstance(motion_type, str):
        return "self_correction"

    final_classifications = {
        MotionCategory.MOTION_TO_COMPEL.value,
        MotionCategory.MOTION_FOR_PROTECTIVE_ORDER.value,
        MotionCategory.MOTION_TO_LIMINE.value,
        MotionCategory.MOTION_TO_EXCLUDE.value,
        MotionCategory.MOTION_TO_MISTRAIL.value
    }

    if motion_type in final_classifications:
        return "__end__"
    elif motion_type == MotionCategory.GENERAL_PROCEDURAL.value:
        return "classify_procedural_motion_type"
    elif motion_type == MotionCategory.DISPOSITIVE.value:
        return "classify_dispositive_motion_type"
    elif motion_type == MotionCategory.POST_TRIAL.value:
        return "classify_post_trial_motion_type"
    elif motion_type == MotionCategory.SUBJECT_SPECIFIC.value:
        return "classify_subject_matter_motion_type"
    else:
        # return "__end__"
        return "self_correction"


def route_notice_type(state: dict) -> str:
    notice_type = state["classification_result"].strip()  # Remove leading/trailing whitespace
    if not isinstance(notice_type, str):
        return "self_correction"

    final_notice_types = {
        # NoticeCategory.GENERAL_NOTICE.value,
        NoticeCategory.NOTICE_OF_FILING.value,
        NoticeCategory.RULE_1_525_NOTICE.value,
        NoticeCategory.NOTICE_OF_CANCELLATION.value,
        NoticeCategory.NOTICE_OF_WITHDRAWAL.value
    }

    if notice_type in final_notice_types:
        return "__end__"
    elif notice_type == NoticeCategory.PROCEDURAL_APPEARANCE_NOTICE.value:
        return "classify_procedural_notice_type"
    elif notice_type == NoticeCategory.HEARING_TRIAL_NOTICE.value:
        return "classify_hearing_notice_type"
    elif notice_type == NoticeCategory.DISCOVERY_EVIDENCE_NOTICE.value:
        return "classify_discovery_evidence_notice_type"
    elif notice_type == NoticeCategory.CASE_STATUS_OUTCOME_NOTICE.value:
        return "classify_case_status_notice_type"
    else:
        # return "__end__"  # Fallback
        return "self_correction"


def add_final_node_routing_to_correction(builder: StateGraph, final_nodes: list[str]):
    """
    Adds a routing edge from each final node to the 'self_correction' node.

    Args:
        builder: The StateGraph builder instance.
        final_nodes: List of node names that are leaf nodes (typically routed to __end__).
    """
    for node_name in final_nodes:
        builder.add_conditional_edges(node_name, lambda state: "self_correction")


def route_order_type(state: dict) -> str:
    order_type = state["classification_result"].strip()
    if not isinstance(order_type, str):
        return "self_correction"

    if order_type in {
        "Case Management Order",
        "Court Order Referral to Mediation",
        "Docket Control Order",
        "Pretrial Conference Order",
        "Scheduling Order",
        "Standing Order of the Judge",
        "Trial Order",
        "Signed Order"
    }:
        return "classify_signed_order_type"

    elif order_type in {
        "Proposed Order",
        "Proposed Pretrial Order"
    }:
        return "classify_proposed_order_type"

    elif order_type in {
        "Joint Status Report",
        "Joint Stipulation",
        "Mediation Report",
        "Settlement Proposal",
        "Stipulated Facts"
    }:
        return "classify_report_or_stipulation_type"
    elif order_type == "Report, Stipulation or Court Record":
        return "classify_report_or_stipulation_type"

    elif order_type == "Court Minutes":
        return "classify_clerk_record_type"

    # return "__end__"
    return "self_correction"